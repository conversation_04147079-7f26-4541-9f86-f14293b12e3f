{"name": "InstantEmailComposer", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "release": "npx react-native build-android --mode=release", "test-release": "react-native run-android --mode=release", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-documents/picker": "^10.1.2", "@types/react-native-vector-icons": "^6.4.18", "react": "19.0.0", "react-native": "0.79.1", "react-native-email-link": "^1.16.1", "react-native-google-mobile-ads": "^15.1.0", "react-native-mail": "^6.1.1", "react-native-pell-rich-editor": "^1.9.0", "react-native-safe-area-context": "^5.5.1", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}