import React, {useState, useEffect} from 'react';
import {Image, Text, View, StyleSheet, Platform} from 'react-native';
import {
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaView,
  BannerAd,
  BannerAdSize,
  NativeMediaAspectRatio,
  TestIds,
} from 'react-native-google-mobile-ads';
// Removed theme import - using inline styles that match the app

// Replace these with your actual AdMob IDs in production
const adUnitIdAndroid = __DEV__
  ? TestIds.BANNER
  : Platform.select({
      ios: 'ca-app-pub-2044352253676532/7235807123',
      android: 'ca-app-pub-2044352253676532/1211078653',
      default: 'ca-app-pub-2044352253676532/1211078653',
    });
const nativeId = __DEV__
  ? TestIds.NATIVE
  : Platform.select({
      ios: 'ca-app-pub-2044352253676532/7532972987',
      android: 'ca-app-pub-2044352253676532/9267011115',
      default: 'ca-app-pub-2044352253676532/9267011115',
    });

export const AdComponent: React.FC = () => {
  const [nativeAd, setNativeAd] = useState<NativeAd>();
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  console.log('AdComponent rendered');

  useEffect(() => {
    const loadAd = async () => {
      try {
        const ad = await NativeAd.createForAdRequest(nativeId, {
          aspectRatio: NativeMediaAspectRatio.SQUARE,
        });
        setNativeAd(ad);
        setHasError(false);
      } catch (error) {
        console.error('Failed to load native ad:', error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadAd();
  }, []); // Empty dependency array ensures this only runs once on mount

  if (isLoading || hasError || !nativeAd) {
    return (
      <View style={styles.adOuterContainer}>
        <View style={[styles.outerContainer]}>
          <BannerAd
            unitId={adUnitIdAndroid}
            size={BannerAdSize.MEDIUM_RECTANGLE}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.adOuterContainer}>
      <View style={[styles.outerContainer, {minHeight: 450}]}>
        <NativeAdView nativeAd={nativeAd} style={styles.adContainer}>
          <View style={[styles.mainContainer]}>
            <View style={styles.headerContainer}>
              {nativeAd.icon && (
                <NativeAsset assetType={NativeAssetType.ICON}>
                  <Image
                    source={{uri: nativeAd.icon.url}}
                    style={[styles.icon]}
                  />
                </NativeAsset>
              )}
              <View style={styles.titleContainer}>
                <NativeAsset assetType={NativeAssetType.HEADLINE}>
                  <Text style={[styles.headline]}>{nativeAd.headline}</Text>
                </NativeAsset>
                {nativeAd.advertiser && (
                  <NativeAsset assetType={NativeAssetType.ADVERTISER}>
                    <Text style={styles.advertiser}>{nativeAd.advertiser}</Text>
                  </NativeAsset>
                )}
                {nativeAd.starRating != null && nativeAd.starRating > 0 && (
                  <NativeAsset assetType={NativeAssetType.STAR_RATING}>
                    <Text style={styles.rating}>
                      Rating: {nativeAd.starRating.toFixed(1)} ★
                    </Text>
                  </NativeAsset>
                )}
              </View>
              <Text style={styles.sponsoredLabel}>Ad</Text>
            </View>

            {/* {<NativeMediaView style={styles.mediaView} />} */}

            {nativeAd.body && (
              <NativeAsset assetType={NativeAssetType.BODY}>
                <Text style={styles.body}>{nativeAd.body}</Text>
              </NativeAsset>
            )}

            {nativeAd.callToAction && (
              <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
                <View style={styles.ctaButton}>
                  <Text style={styles.ctaText}>{nativeAd.callToAction}</Text>
                </View>
              </NativeAsset>
            )}
          </View>
        </NativeAdView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    width: '100%',
    backgroundColor: 'transparent',
  },
  adContainer: {
    // padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e9ecef',
    width: '100%',
    backgroundColor: '#ffffff',
  },
  mainContainer: {
    // width: '100%',
    margin: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginBottom: 12,
    minHeight: 60,
    paddingVertical: 8,
    overflow: 'hidden',
  },
  icon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 16,
  },
  titleContainer: {
    flex: 1,
  },
  headline: {
    fontSize: 18,
    fontWeight: '600',
    color: '#202124',
    marginBottom: 4,
  },
  advertiser: {
    fontSize: 12,
    color: '#adb5bd',
    marginTop: 4,
  },
  sponsoredLabel: {
    fontSize: 12,
    color: '#adb5bd',
    backgroundColor: '#4285F420', // 20% opacity
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
    marginLeft: 8,
  },
  mediaView: {
    width: '100%',
    aspectRatio: 16 / 9,
    maxHeight: 250,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  body: {
    fontSize: 16,
    color: '#adb5bd',
    marginTop: 16,
    marginBottom: 16,
    lineHeight: 22,
    flexShrink: 1,
    minHeight: 60,
  },
  ctaButton: {
    backgroundColor: '#4285F4',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  ctaText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  rating: {
    fontSize: 12,
    color: '#4285F4',
    fontWeight: '500',
    marginTop: 4,
  },
  adOuterContainer: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 24,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
});
