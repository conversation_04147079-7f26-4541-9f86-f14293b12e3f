import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, FlatList, Alert, Dimensions, useWindowDimensions } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { EmailTemplate, RecentAttachment } from '../utils/storage';
import RecentAttachments from './RecentAttachments';

interface TemplateBarProps {
  templates: EmailTemplate[];
  recentAttachments: RecentAttachment[];
  onAddAttachment: () => void;
  onSelectAttachment: (attachment: RecentAttachment) => void;
  onSelectTemplate: (template: EmailTemplate) => void;
  onCreateTemplate: () => void;
  onRemoveTemplate: (templateId: string) => void;
  onClearRecentAttachments?: () => void;
  onSend: () => void;
}

const TemplateBar: React.FC<TemplateBarProps> = ({
  templates,
  recentAttachments,
  onAddAttachment,
  onSelectAttachment,
  onSelectTemplate,
  onCreateTemplate,
  onRemoveTemplate,
  onClearRecentAttachments,
  onSend,
}) => {
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [attachmentModalVisible, setAttachmentModalVisible] = useState(false);

  return (
    <View style={styles.container}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setAttachmentModalVisible(true)}
        >
          <Icon name="attach-file" size={22} color="#5f6368" />
          <Text style={styles.toolbarButtonText}>Attach</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setTemplateModalVisible(true)}
        >
          <Icon name="description" size={22} color="#5f6368" />
          <Text style={styles.toolbarButtonText}>Templates</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={onCreateTemplate}
        >
          <Icon name="save" size={22} color="#5f6368" />
          <Text style={styles.toolbarButtonText}>Save</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.sendButton}
          onPress={onSend}
        >
          <Icon name="send" size={20} color="#ffffff" />
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>

      {/* Template Selection Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={templateModalVisible}
        onRequestClose={() => setTemplateModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select a Template</Text>
              <TouchableOpacity onPress={() => setTemplateModalVisible(false)}>
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            <FlatList
              data={templates}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View style={styles.templateItem}>
                  <TouchableOpacity
                    style={styles.templateContent}
                    onPress={() => {
                      onSelectTemplate(item);
                      setTemplateModalVisible(false);
                    }}
                  >
                    <Text style={styles.templateName}>{item.name}</Text>
                    <Text style={styles.templateSubject} numberOfLines={1}>
                      {item.subject}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => {
                      Alert.alert(
                        "Remove Template",
                        `Are you sure you want to remove "${item.name}"?`,
                        [
                          {
                            text: "Cancel",
                            style: "cancel"
                          },
                          {
                            text: "Remove",
                            onPress: () => onRemoveTemplate(item.id),
                            style: "destructive"
                          }
                        ]
                      );
                    }}
                  >
                    <Icon name="close" size={16} color="#5f6368" />
                  </TouchableOpacity>
                </View>
              )}
              ListFooterComponent={
                <TouchableOpacity
                  style={[styles.templateItem, styles.addNewItem]}
                  onPress={() => {
                    onCreateTemplate();
                    setTemplateModalVisible(false);
                  }}
                >
                  <Text style={styles.addNewText}>+ Add New Template</Text>
                </TouchableOpacity>
              }
            />
          </View>
        </View>
      </Modal>

      {/* Recent Attachments Modal */}
      <RecentAttachments
        attachments={recentAttachments}
        onSelectAttachment={onSelectAttachment}
        onAddNewAttachment={onAddAttachment}
        onClearAttachments={onClearRecentAttachments}
        visible={attachmentModalVisible}
        onClose={() => setAttachmentModalVisible(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
    flexWrap: 'wrap',
    },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  toolbarButtonText: {
    color: '#5f6368',
    fontSize: 14,
    marginLeft: 4,
    fontWeight: '500',
  },
  sendButton: {
    flexGrow: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#4285F4',
    marginLeft: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  sendButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#202124',
  },
  closeButton: {
    fontSize: 14,
    color: '#5f6368',
  },
  templateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f5',
  },
  templateContent: {
    flex: 1,
    paddingHorizontal: 4,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#202124',
    marginBottom: 4,
  },
  templateSubject: {
    fontSize: 14,
    color: '#5f6368',
  },
  removeButton: {
    padding: 8,
    borderRadius: 20,
  },
  addNewItem: {
    paddingVertical: 18,
    alignItems: 'center',
    borderBottomWidth: 0,
    marginTop: 8,
  },
  addNewText: {
    fontSize: 15,
    color: '#4285F4',
    fontWeight: '600',
  },
});

export default TemplateBar;
