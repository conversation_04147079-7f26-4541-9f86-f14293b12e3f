import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RecentRecipient } from '../utils/storage';

interface RecentRecipientsProps {
  recipients: RecentRecipient[];
  onSelectRecipient: (email: string) => void;
  onClearRecipients?: () => void;
  visible: boolean;
  position?: { top: number; left: number; width: number };
}

const RecentRecipients: React.FC<RecentRecipientsProps> = ({
  recipients,
  onSelectRecipient,
  onClearRecipients,
  visible,
  position,
}) => {
  // Use state to track if an action is in progress
  const [isActionInProgress, setIsActionInProgress] = useState(false);

  // Reset action state when visibility changes
  useEffect(() => {
    if (!visible) {
      setIsActionInProgress(false);
    }
  }, [visible]);

  // Don't render if not visible or no recipients (unless action in progress)
  if ((!visible && !isActionInProgress) || recipients.length === 0) {
    return null;
  }

  // Calculate dropdown position based on input field position
  const dropdownStyle = position ? {
    position: 'absolute' as 'absolute',
    top: position.top,
    left: position.left,
    width: position.width,
    zIndex: 1000,
  } : {};

  // Get first letter of email for avatar
  const getInitial = (email: string): string => {
    return email.charAt(0).toUpperCase();
  };

  // Handle recipient selection
  const handleSelectRecipient = (email: string) => {
    setIsActionInProgress(true);
    // Call the parent handler
    onSelectRecipient(email);
    // Reset action state after a short delay
    setTimeout(() => {
      setIsActionInProgress(false);
    }, 100);
  };

  // Handle clear all
  const handleClearAll = () => {
    if (onClearRecipients) {
      setIsActionInProgress(true);
      // Call the parent handler
      onClearRecipients();
      // Reset action state after a short delay
      setTimeout(() => {
        setIsActionInProgress(false);
      }, 100);
    }
  };

  return (
    <View style={[styles.container, dropdownStyle]}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Recent Recipients</Text>
      </View>
      <FlatList
        data={recipients}
        keyExtractor={(item) => item.email}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.recipientItem}
            onPress={() => handleSelectRecipient(item.email)}
          >
          <View style={styles.avatarCircle}>
            <Text style={styles.avatarText}>{getInitial(item.email)}</Text>
          </View>
            <View style={styles.emailContainer}>
              <Text style={styles.emailText}>{item.email}</Text>
              <Text style={styles.emailHint}>Tap to select</Text>
            </View>
          </TouchableOpacity>
        )}
        style={styles.list}
        scrollEnabled={true}
        nestedScrollEnabled={true}
        ListFooterComponent={
          onClearRecipients ? (
            <TouchableOpacity
              style={styles.clearAllButton}
              onPress={handleClearAll}
            >
              <Icon name="delete-sweep" size={16} color="#EA4335" />
              <Text style={styles.clearAllText}>Clear all recipients</Text>
            </TouchableOpacity>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 320,
    zIndex: 1000,
    overflow: 'hidden',
  },
  header: {
    backgroundColor: '#f8f9fa',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4285F4',
  },
  list: {
    maxHeight: 250,
  },
  recipientItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f5',
  },
  avatarCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4285F4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  emailContainer: {
    flex: 1,
  },
  emailText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#202124',
  },
  emailHint: {
    fontSize: 12,
    color: '#5f6368',
    marginTop: 2,
  },
  clearAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    backgroundColor: '#f8f9fa',
  },
  clearAllText: {
    fontSize: 14,
    color: '#EA4335',
    fontWeight: '500',
    marginLeft: 6,
  },
});

export default RecentRecipients;
