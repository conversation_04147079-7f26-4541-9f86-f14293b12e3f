import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { EmailTemplate } from '../utils/storage';
import RichTextEditor, { RichTextEditorRef } from './RichTextEditor';

interface SaveTemplateModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (template: EmailTemplate) => void;
  initialSubject: string;
  initialBody: string;
}

const SaveTemplateModal: React.FC<SaveTemplateModalProps> = ({
  visible,
  onClose,
  onSave,
  initialSubject,
  initialBody,
}) => {
  const [templateName, setTemplateName] = useState('');
  const [templateSubject, setTemplateSubject] = useState(initialSubject);
  const [templateBody, setTemplateBody] = useState(initialBody);
  const [error, setError] = useState('');
  const [editorMounted, setEditorMounted] = useState(false);
  const richTextEditorRef = useRef<RichTextEditorRef>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  // Update state when props change and modal becomes visible
  useEffect(() => {
    if (visible) {
      setTemplateSubject(initialSubject);
      setTemplateBody(initialBody);
      setEditorMounted(true);
    }
  }, [initialSubject, initialBody, visible]);

  // When the editor is mounted and we have initial content, set it
  useEffect(() => {
    if (editorMounted && visible && initialBody && richTextEditorRef.current) {
      // Slight delay to ensure editor is fully initialized
      setTimeout(() => {
        if (richTextEditorRef.current) {
          richTextEditorRef.current.setContentHTML(initialBody);
        }
      }, 100);
    }
  }, [editorMounted, visible, initialBody]);

  const handleSave = async () => {
    // Validate inputs
    if (!templateName.trim()) {
      setError('Template name is required');
      return;
    }

    if (!templateSubject.trim()) {
      setError('Subject is required');
      return;
    }

    let bodyContent = templateBody;
    
    // If we're using the rich text editor, get its HTML content
    if (richTextEditorRef.current) {
      try {
        bodyContent = await richTextEditorRef.current.getContentHTML() || '';
      } catch (err) {
        console.error('Error getting rich text content:', err);
      }
    }

    if (!bodyContent.trim()) {
      setError('Body is required');
      return;
    }

    // Create new template
    const newTemplate: EmailTemplate = {
      id: `custom-${Date.now()}`, // Generate a unique ID
      name: templateName.trim(),
      subject: templateSubject.trim(),
      body: bodyContent.trim(),
    };

    // Save template and close modal
    onSave(newTemplate);
    resetForm();
  };

  const resetForm = () => {
    setTemplateName('');
    setTemplateSubject(initialSubject);
    setTemplateBody(initialBody);
    setError('');
    setEditorMounted(false);
    onClose();
  };

  const handleCancel = () => {
    resetForm();
  };

  // Handle cursor position for rich text editor to scroll properly
  const handleCursorPosition = useCallback((scrollY: number) => {
    if (scrollViewRef.current && typeof scrollY === 'number') {
      scrollViewRef.current.scrollTo({
        y: scrollY,
        animated: true
      });
    }
  }, []);
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={handleCancel}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.modalOverlay}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
      >
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Save as Template</Text>
            <TouchableOpacity onPress={handleCancel} style={styles.closeButtonContainer}>
              <Icon name="close" size={20} color="#5f6368" />
            </TouchableOpacity>
          </View>

          {error ? (
            <View style={styles.errorContainer}>
              <Icon name="error-outline" size={16} color="#d93025" />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : null}

          <ScrollView 
            style={styles.formScrollView} 
            ref={scrollViewRef}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={styles.scrollViewContent}
            nestedScrollEnabled={true}
          >
            <View style={styles.formGroup}>
              <Text style={styles.label}>Template Name</Text>
              <View style={styles.inputContainer}>
                <Icon name="bookmark-border" size={20} color="#4285F4" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter a name for this template"
                  placeholderTextColor="#5f6368"
                  value={templateName}
                  onChangeText={setTemplateName}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Subject</Text>
              <View style={styles.inputContainer}>
                <Icon name="subject" size={20} color="#4285F4" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Email subject"
                  placeholderTextColor="#5f6368"
                  value={templateSubject}
                  onChangeText={setTemplateSubject}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Body</Text>
              <View style={styles.richEditorContainer}>
                <RichTextEditor
                  ref={richTextEditorRef}
                  initialContent=""
                  placeholder="Compose email content"
                  onChange={setTemplateBody}
                  height={150}
                  onCursorPosition={handleCursorPosition}
                />
              </View>
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Icon name="save" size={18} color="#ffffff" style={styles.saveButtonIcon} />
              <Text style={styles.saveButtonText}>Save Template</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '85%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#4285F4',
  },
  closeButtonContainer: {
    padding: 4,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fce8e6',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#d93025',
    fontSize: 14,
    marginLeft: 8,
  },
  formScrollView: {
    maxHeight: Platform.OS === 'ios' ? 350 : 400,
    marginBottom: 10,
    flexGrow: 0,
  },
  scrollViewContent: {
    paddingBottom: 20, // Add padding to the content
    flexGrow: 1,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#5f6368',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  inputIcon: {
    padding: 12,
  },
  input: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    color: '#202124',
  },
  richEditorContainer: {
    minHeight: 200,
    marginBottom: 20,
    borderRadius: 8,
    overflow: 'hidden',
  },
  editorContent: {
    flex: 1,
    height: 'auto',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 10,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingTop: 10,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  cancelButtonText: {
    color: '#5f6368',
    fontWeight: '600',
    fontSize: 14,
  },
  saveButton: {
    backgroundColor: '#4285F4',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  saveButtonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default SaveTemplateModal;
