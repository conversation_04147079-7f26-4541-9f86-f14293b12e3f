import React, { useRef, forwardRef, useImperativeHandle, useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
  PermissionsAndroid,
} from 'react-native';
// @ts-ignore
import { RichEditor, RichToolbar, actions } from 'react-native-pell-rich-editor';

export interface RichTextEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (text: string) => void;
  height?: number;
  containerStyle?: any;
  disabled?: boolean;
  onCursorPosition?: (scrollY: number) => void;
}

export interface RichTextEditorRef {
  setContentHTML: (html: string) => void;
  getContentHTML: () => Promise<string>;
  insertLink: (title: string, url: string) => void;
  insertHTML: (html: string) => void;
  focusContentEditor: () => void;
  blurContentEditor: () => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(
  (
    {
      initialContent = '',
      placeholder = 'Start typing...',
      onChange,
      height = 200,
      containerStyle,
      disabled = false,
      onCursorPosition,
    },
    ref
  ) => {
    const richTextRef = useRef<any>(null);
    const [editorReady, setEditorReady] = useState(false);
    const [showUrlModal, setShowUrlModal] = useState(false);
    const [imageUrl, setImageUrl] = useState('');
    
    // Update content when initialContent prop changes
    useEffect(() => {
      if (richTextRef.current && editorReady && initialContent) {
        // Use setTimeout to ensure we're not updating during a render cycle
        setTimeout(() => {
          richTextRef.current.setContentHTML(initialContent);
        }, 0);
      }
    }, [initialContent, editorReady]);

    useImperativeHandle(ref, () => ({
      setContentHTML: (html: string) => {
        if (richTextRef.current && editorReady) {
          richTextRef.current.setContentHTML(html);
        }
      },
      getContentHTML: async () => {
        if (richTextRef.current && editorReady) {
          return richTextRef.current.getContentHtml();
        }
        return initialContent || '';
      },
      insertLink: (title: string, url: string) => {
        if (richTextRef.current && editorReady) {
          richTextRef.current.insertLink(title, url);
        }
      },
      insertHTML: (html: string) => {
        if (richTextRef.current && editorReady) {
          richTextRef.current.insertHTML(html);
        }
      },
      focusContentEditor: () => {
        if (richTextRef.current && editorReady) {
          richTextRef.current.focusContentEditor();
        }
      },
      blurContentEditor: () => {
        if (richTextRef.current && editorReady) {
          richTextRef.current.blurContentEditor();
        }
      },
    }));

    const requestCameraPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
              title: 'Camera Permission',
              message: 'App needs camera permission to take pictures.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        } catch (err) {
          console.warn(err);
          return false;
        }
      }
      return true; // iOS doesn't need explicit permission for the image picker
    };


    const handleUrlSubmit = () => {
      if (imageUrl && imageUrl.trim() !== '') {
        if (richTextRef.current && editorReady) {
          richTextRef.current.insertImage(imageUrl.trim());
        }
      }
      setShowUrlModal(false);
    };

    const onEditorInitialized = () => {
      console.log('Editor initialized');
      setEditorReady(true);
      // Set initial content after editor is initialized
      if (richTextRef.current && initialContent) {
        setTimeout(() => {
          richTextRef.current.setContentHTML(initialContent);
        }, 50);
      }
    };

    const handleCursorPosition = (scrollY: number) => {
      if (onCursorPosition && typeof onCursorPosition === 'function') {
        onCursorPosition(scrollY);
      }
    };

    // Prepare actions array - avoid using the array directly in the JSX to prevent React Fabric issues
    const editorActions = [
      actions.setBold,
      actions.setItalic,
      actions.setUnderline,
      actions.insertBulletsList,
      actions.insertOrderedList,
      actions.insertLink,
      actions.setStrikethrough,
      actions.removeFormat,
      actions.indent,
      actions.outdent,
      actions.undo,
      actions.redo,
    ];

    return (
      <View style={[styles.container, containerStyle]}>
        <ScrollView>
          <RichEditor
            ref={richTextRef}
            initialContentHTML=""
            onChange={onChange}
            placeholder={placeholder}
            useContainer={true}
            initialHeight={height}
            disabled={disabled}
            editorInitializedCallback={onEditorInitialized}
            onCursorPosition={handleCursorPosition}
            editorStyle={{
              backgroundColor: '#ffffff',
              color: '#202124',
              placeholderColor: '#adb5bd',
              contentCSSText: `font-size: 16px; min-height: ${height}px;`,
            }}
          />
        </ScrollView>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.toolbarContainer}
        >
          <RichToolbar
            style={styles.toolbar}
            editor={richTextRef}
            disabled={disabled}
            iconTint="#4285F4"
            selectedIconTint="#4285F4"
            disabledIconTint="#adb5bd"
            actions={editorActions}
            selectedButtonStyle={{ backgroundColor: '#e9ecef' }}
          />
        </KeyboardAvoidingView>
       
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderColor: '#e9ecef',
    borderWidth: 1,
    borderRadius: 8,
  },
  toolbarContainer: {
    position: 'relative',
    bottom: 0,
    left: 0,
    right: 0,
  },
  toolbar: {
    backgroundColor: '#f8f9fa',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  urlInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#6c757d',
  },
  insertButton: {
    backgroundColor: '#4285F4',
  },
  buttonText: {
    color: 'white',
    fontWeight: '500',
  },
});

export default RichTextEditor; 