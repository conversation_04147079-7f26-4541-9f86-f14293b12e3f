import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Attachment } from '../utils/attachments';

interface AttachmentManagerProps {
  attachments: Attachment[];
  onRemoveAttachment: (index: number) => void;
}

const AttachmentManager: React.FC<AttachmentManagerProps> = ({
  attachments,
  onRemoveAttachment,
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (attachments.length === 0) {
    return null; // Don't show anything if no attachments
  }

  // Function to determine the icon based on file type
  const getFileIcon = (type: string): string => {
    if (type.startsWith('image/')) return 'image';
    if (type.startsWith('video/')) return 'videocam';
    if (type.startsWith('audio/')) return 'audiotrack';
    if (type.includes('pdf')) return 'picture-as-pdf';
    if (type.includes('word') || type.includes('document')) return 'description';
    if (type.includes('excel') || type.includes('sheet')) return 'table-chart';
    if (type.includes('text/')) return 'text-snippet';
    return 'insert-drive-file';
  };

  // Function to get color based on file type
  const getFileColor = (type: string): string => {
    if (type.startsWith('image/')) return '#34A853'; // Green for images
    if (type.startsWith('video/')) return '#EA4335'; // Red for videos
    if (type.startsWith('audio/')) return '#FBBC05'; // Yellow for audio
    if (type.includes('pdf')) return '#EA4335'; // Red for PDFs
    if (type.includes('word') || type.includes('document')) return '#4285F4'; // Blue for documents
    if (type.includes('excel') || type.includes('sheet')) return '#34A853'; // Green for spreadsheets
    return '#5f6368'; // Default gray
  };

  return (
    <View style={styles.container}>
      {attachments.length > 0 && (
        <Text style={styles.sectionTitle}>Attachments ({attachments.length})</Text>
      )}
      {attachments.map((item, index) => (
        <View key={`attachment-${index}`} style={styles.attachmentItem}>
          <View style={[styles.iconContainer, { backgroundColor: `${getFileColor(item.type)}20` }]}>
            <Icon name={getFileIcon(item.type)} size={20} color={getFileColor(item.type)} />
          </View>
          <View style={styles.attachmentInfo}>
            <Text style={styles.attachmentName} numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={styles.attachmentSize}>{formatFileSize(item.size)}</Text>
          </View>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => onRemoveAttachment(index)}
          >
            <Icon name="close" size={16} color="#5f6368" />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4285F4',
    marginBottom: 8,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  attachmentInfo: {
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#202124',
  },
  attachmentSize: {
    fontSize: 12,
    color: '#5f6368',
    marginTop: 2,
  },
  removeButton: {
    padding: 8,
    borderRadius: 16,
  },
});

export default AttachmentManager;
