import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList, Modal } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RecentAttachment } from '../utils/storage';

interface RecentAttachmentsProps {
  attachments: RecentAttachment[];
  onSelectAttachment: (attachment: RecentAttachment) => void;
  onAddNewAttachment: () => void;
  onClearAttachments?: () => void;
  visible: boolean;
  onClose: () => void;
}

const RecentAttachments: React.FC<RecentAttachmentsProps> = ({
  attachments,
  onSelectAttachment,
  onAddNewAttachment,
  onClearAttachments,
  visible,
  onClose,
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string): string => {
    if (type.startsWith('image/')) return 'image';
    if (type.startsWith('video/')) return 'videocam';
    if (type.startsWith('audio/')) return 'audiotrack';
    if (type.includes('pdf')) return 'picture-as-pdf';
    if (type.includes('word') || type.includes('document')) return 'description';
    if (type.includes('excel') || type.includes('sheet')) return 'table-chart';
    if (type.includes('text/')) return 'text-snippet';
    return 'insert-drive-file';
  };

  // Function to get color based on file type
  const getFileColor = (type: string): string => {
    if (type.startsWith('image/')) return '#34A853'; // Green for images
    if (type.startsWith('video/')) return '#EA4335'; // Red for videos
    if (type.startsWith('audio/')) return '#FBBC05'; // Yellow for audio
    if (type.includes('pdf')) return '#EA4335'; // Red for PDFs
    if (type.includes('word') || type.includes('document')) return '#4285F4'; // Blue for documents
    if (type.includes('excel') || type.includes('sheet')) return '#34A853'; // Green for spreadsheets
    return '#5f6368'; // Default gray
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Recent Attachments</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButtonContainer}>
              <Icon name="close" size={20} color="#5f6368" />
            </TouchableOpacity>
          </View>

          {attachments.length > 0 ? (
            <>
              <FlatList
                data={attachments}
                keyExtractor={(item, index) => `attachment-${index}-${item.name}`}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.attachmentItem}
                    onPress={() => {
                      onSelectAttachment(item);
                      onClose();
                    }}
                  >
                    <View style={[styles.iconContainer, { backgroundColor: `${getFileColor(item.type)}20` }]}>
                      <Icon name={getFileIcon(item.type)} size={24} color={getFileColor(item.type)} />
                    </View>
                    <View style={styles.attachmentInfo}>
                      <Text style={styles.attachmentName} numberOfLines={1}>
                        {item.name}
                      </Text>
                      <Text style={styles.attachmentSize}>{formatFileSize(item.size)}</Text>
                    </View>
                  </TouchableOpacity>
                )}
                ListFooterComponent={
                  <>
                    <TouchableOpacity
                      style={[styles.attachmentItem, styles.addNewItem]}
                      onPress={() => {
                        onAddNewAttachment();
                        onClose();
                      }}
                    >
                      <View style={[styles.iconContainer, { backgroundColor: '#4285F420' }]}>
                        <Icon name="add" size={24} color="#4285F4" />
                      </View>
                      <Text style={styles.addNewText}>Add New Attachment</Text>
                    </TouchableOpacity>
                    
                    {onClearAttachments && (
                      <TouchableOpacity
                        style={styles.clearButton}
                        onPress={() => {
                          onClearAttachments();
                          onClose();
                        }}
                      >
                        <Icon name="delete-sweep" size={18} color="#EA4335" style={styles.clearButtonIcon} />
                        <Text style={styles.clearButtonText}>Clear Recent Attachments</Text>
                      </TouchableOpacity>
                    )}
                  </>
                }
              />
            </>
          ) : (
            <View style={styles.emptyContainer}>
              <Icon name="attachment" size={48} color="#e9ecef" style={styles.emptyIcon} />
              <Text style={styles.emptyText}>No recent attachments</Text>
              <TouchableOpacity
                style={styles.emptyAddButton}
                onPress={() => {
                  onAddNewAttachment();
                  onClose();
                }}
              >
                <Icon name="add" size={18} color="#ffffff" style={styles.emptyAddButtonIcon} />
                <Text style={styles.emptyAddButtonText}>Add New Attachment</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4285F4',
  },
  closeButtonContainer: {
    padding: 4,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginVertical: 4,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  attachmentInfo: {
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    color: '#202124',
    fontWeight: '500',
  },
  attachmentSize: {
    fontSize: 12,
    color: '#5f6368',
    marginTop: 2,
  },
  addNewItem: {
    marginTop: 8,
    borderBottomWidth: 0,
    borderStyle: 'dashed',
  },
  addNewText: {
    fontSize: 14,
    color: '#4285F4',
    fontWeight: '600',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f1f3f5',
  },
  clearButtonIcon: {
    marginRight: 8,
  },
  clearButtonText: {
    fontSize: 14,
    color: '#EA4335',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#5f6368',
    marginBottom: 24,
  },
  emptyAddButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4285F4',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  emptyAddButtonIcon: {
    marginRight: 8,
  },
  emptyAddButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default RecentAttachments;
