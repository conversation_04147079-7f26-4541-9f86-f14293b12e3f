import React from 'react';
import {StyleSheet, View, Dimensions, ScrollView, Text, Platform} from 'react-native';
import {AdComponent} from '../components/AdComponent';
import {AppCard} from '../components/AboutUs/AppCard';
import AboutUs from '../components/AboutUs/AboutUs';

const {width: screenWidth} = Dimensions.get('window');

export default function AboutUsPage() {

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        bounces={true}>
        <AdComponent
        />

        <View style={styles.spacer} />
          <AboutUs />
        <View style={styles.spacer} />

        {/* Our Other Apps Section */}
        <View style={styles.appsSection}>
          <Text style={styles.appsSectionTitle}>
            🚀 Discover Our Other Apps
          </Text>
          <Text style={styles.appsSectionSubtitle}>
            Explore more amazing tools we've built for you
          </Text>

          <View style={styles.appsGrid}>
            <AppCard
              icon="💬"
              title="Quick Chat"
              description="WhatsApp without saving numbers"
              packageId="com.quickchat.plustech"
            />
            <AppCard
              icon="🎨"
              title="SVG Viewer Pro"
              description="Professional SVG file viewer and editor"
              packageId="com.svgviewerapp"
            />
            <AppCard
              icon="💭"
              title="Friendly Chat"
              description="Meet new people anonymously with no login"
              packageId="com.friendlychatclient"
            />
            <AppCard
              icon="🔋"
              title="Battery Charger"
              description="Monitor your battery charging stats"
              packageId="com.chargingstats"
            />
            <AppCard
              icon="🏦"
              title="Bank Balance"
              description="Check bank balance via SMS"
              packageId="com.banksms"
            />
            <AppCard
              icon="📱"
              title="QR Generator"
              description="Create and scan QR codes instantly"
              packageId="com.pt.qrcodegenerator"
            />
            <AppCard
              icon="📧"
              title="Mail Templates Pro"
              description="Professional email templates"
              packageId="com.instantemailcomposer"
            />
          </View>
        </View>

        <View style={styles.spacer} />
        <AdComponent
        />

        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  loadingContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6c757d',
  },
  errorContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  errorText: {
    fontSize: 20,
    color: '#dc3545',
    fontWeight: 'bold',
    marginBottom: 16,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#6c757d',
  },
  adFreeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 24,
  },
  adFreeText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4285F4',
    marginBottom: 16,
    textAlign: 'center',
  },
  adFreeSubtext: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
  },
  bannerAd: {
    width: screenWidth,
    alignSelf: 'center',
    marginVertical: 16,
  },
  secondaryBannerAd: {
    width: screenWidth,
    alignSelf: 'center',
  },
  spacer: {
    height: 24,
    backgroundColor: '#e9ecef',
    width: '100%',
  },
  bottomPadding: {
    height: 48,
    width: '100%',
  },
  // Apps Section Styles
  appsSection: {
    width: '100%',
    paddingHorizontal: 24,
    paddingVertical: 32,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  appsSectionTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#4285F4',
    textAlign: 'center',
    marginBottom: 16,
    letterSpacing: -1,
  },
  appsSectionSubtitle: {
    fontSize: 18,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
    letterSpacing: 0,
  },
  appsGrid: {
    gap: 16,
  },
});
