import * as DocumentPicker from '@react-native-documents/picker';

export interface Attachment {
  path?: string;
  uri: string;
  type: string;
  name: string;
  size: number;
}

/**
 * Pick a document from the device
 * @returns Promise with the selected document
 */
export const pickDocument = async (): Promise<Attachment | null> => {
  try {
    console.log('Picking document...');
    const results = await DocumentPicker.pick({
      mode: 'open',
      allowMultiSelection: false,
    });
    console.log('Document picked:', results);

    if (results && results.length > 0) {
      return formatAttachment(results[0]);
    }

    return null;
  } catch (err) {
    console.log('Error picking document:', err);

    if (err && typeof err === 'object' && 'code' in err &&
        err.code === DocumentPicker.errorCodes.OPERATION_CANCELED) {
      // User cancelled the picker
      console.log('User cancelled document picker');
    } else {
      console.error('Error picking document:', err);
    }
    return null;
  }
};

/**
 * Format document picker result to attachment format
 * @param document Document picker result
 * @returns Formatted attachment
 */
const formatAttachment = (document: any): Attachment => {
  return {
    uri: document.uri || '',
    type: document.type || getMimeType(document.name || ''),
    name: document.name || 'Unknown file',
    size: document.size || 0,
  };
};

/**
 * Get MIME type from file name
 * @param fileName File name with extension
 * @returns MIME type string
 */
export const getMimeType = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase();

  switch (ext) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'pdf':
      return 'application/pdf';
    case 'doc':
      return 'application/msword';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'xls':
      return 'application/vnd.ms-excel';
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'txt':
      return 'text/plain';
    default:
      return 'application/octet-stream';
  }
};
