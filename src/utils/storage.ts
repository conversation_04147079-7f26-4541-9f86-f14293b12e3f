import AsyncStorage from '@react-native-async-storage/async-storage';

const RECENT_RECIPIENTS_KEY = 'recent_recipients';
const EMAIL_TEMPLATES_KEY = 'email_templates';
const RECENT_ATTACHMENTS_KEY = 'recent_attachments';
const MAX_RECENT_RECIPIENTS = 5;
const MAX_RECENT_ATTACHMENTS = 5;

export interface RecentRecipient {
  email: string;
  timestamp: number;
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
}

export interface RecentAttachment {
  uri: string;
  type: string;
  name: string;
  size: number;
  timestamp: number;
}

/**
 * Add a new email to recent recipients
 * @param email Email address to add
 */
export const addRecentRecipient = async (email: string): Promise<void> => {
  try {
    // Validate email format
    if (!email || !email.includes('@')) {
      return;
    }

    // Get existing recipients
    const existingRecipients = await getRecentRecipients();

    // Create new recipient object
    const newRecipient: RecentRecipient = {
      email,
      timestamp: Date.now(),
    };

    // Remove if already exists (to avoid duplicates)
    const filteredRecipients = existingRecipients.filter(
      recipient => recipient.email.toLowerCase() !== email.toLowerCase()
    );

    // Add new recipient at the beginning
    const updatedRecipients = [newRecipient, ...filteredRecipients];

    // Keep only the most recent MAX_RECENT_RECIPIENTS
    const trimmedRecipients = updatedRecipients.slice(0, MAX_RECENT_RECIPIENTS);

    // Save to AsyncStorage
    await AsyncStorage.setItem(
      RECENT_RECIPIENTS_KEY,
      JSON.stringify(trimmedRecipients)
    );
  } catch (error) {
    console.error('Error adding recent recipient:', error);
  }
};

/**
 * Get list of recent recipients
 * @returns Array of recent recipients sorted by most recent first
 */
export const getRecentRecipients = async (): Promise<RecentRecipient[]> => {
  try {
    const recipientsJson = await AsyncStorage.getItem(RECENT_RECIPIENTS_KEY);

    if (!recipientsJson) {
      return [];
    }

    const recipients: RecentRecipient[] = JSON.parse(recipientsJson);

    // Sort by timestamp (most recent first)
    return recipients.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error getting recent recipients:', error);
    return [];
  }
};

/**
 * Clear all recent recipients
 */
export const clearRecentRecipients = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(RECENT_RECIPIENTS_KEY);
  } catch (error) {
    console.error('Error clearing recent recipients:', error);
  }
};

/**
 * Get all email templates
 * @returns Array of email templates
 */
export const getEmailTemplates = async (): Promise<EmailTemplate[]> => {
  try {
    const templatesJson = await AsyncStorage.getItem(EMAIL_TEMPLATES_KEY);

    if (!templatesJson) {
      // Return default templates if none exist
      return getDefaultTemplates();
    }

    const templates: EmailTemplate[] = JSON.parse(templatesJson);
    return templates;
  } catch (error) {
    console.error('Error getting email templates:', error);
    return getDefaultTemplates();
  }
};

/**
 * Save email templates
 * @param templates Array of email templates to save
 */
export const saveEmailTemplates = async (templates: EmailTemplate[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(
      EMAIL_TEMPLATES_KEY,
      JSON.stringify(templates)
    );
  } catch (error) {
    console.error('Error saving email templates:', error);
  }
};

/**
 * Add a new email template
 * @param template Email template to add
 */
export const addEmailTemplate = async (template: EmailTemplate): Promise<void> => {
  try {
    const templates = await getEmailTemplates();

    // Check if template with same ID exists
    const existingIndex = templates.findIndex(t => t.id === template.id);

    if (existingIndex >= 0) {
      // Update existing template
      templates[existingIndex] = template;
    } else {
      // Add new template
      templates.push(template);
    }

    await saveEmailTemplates(templates);
  } catch (error) {
    console.error('Error adding email template:', error);
  }
};

/**
 * Delete an email template
 * @param templateId ID of the template to delete
 */
export const deleteEmailTemplate = async (templateId: string): Promise<void> => {
  try {
    const templates = await getEmailTemplates();
    const filteredTemplates = templates.filter(t => t.id !== templateId);

    await saveEmailTemplates(filteredTemplates);
  } catch (error) {
    console.error('Error deleting email template:', error);
  }
};

/**
 * Get default email templates
 * @returns Array of default email templates
 */
export const getDefaultTemplates = (): EmailTemplate[] => {
  return [
    {
      id: 'meeting-request',
      name: 'Meeting Request',
      subject: 'Request for Meeting',
      body: 'Hello,\n\nI would like to schedule a meeting to discuss [topic]. Are you available sometime this week?\n\nBest regards,\n[Your Name]'
    },
    {
      id: 'thank-you',
      name: 'Thank You',
      subject: 'Thank You',
      body: 'Dear [Name],\n\nThank you for your time and assistance. I really appreciate your help with [topic].\n\nBest regards,\n[Your Name]'
    },
    {
      id: 'follow-up',
      name: 'Follow-up',
      subject: 'Follow-up: [Topic]',
      body: 'Hello,\n\nI wanted to follow up on our previous conversation about [topic]. Have you had a chance to review the information I sent?\n\nBest regards,\n[Your Name]'
    }
  ];
};

/**
 * Add a new attachment to recent attachments
 * @param attachment Attachment to add
 */
export const addRecentAttachment = async (attachment: Omit<RecentAttachment, 'timestamp'>): Promise<void> => {
  try {
    // Validate attachment
    if (!attachment || !attachment.uri) {
      return;
    }

    // Get existing attachments
    const existingAttachments = await getRecentAttachments();

    // Create new attachment object with timestamp
    const newAttachment: RecentAttachment = {
      ...attachment,
      timestamp: Date.now(),
    };

    // Remove if already exists (to avoid duplicates)
    // We consider attachments with the same name and size to be duplicates
    const filteredAttachments = existingAttachments.filter(
      item => !(item.name === attachment.name && item.size === attachment.size)
    );

    // Add new attachment at the beginning
    const updatedAttachments = [newAttachment, ...filteredAttachments];

    // Keep only the most recent MAX_RECENT_ATTACHMENTS
    const trimmedAttachments = updatedAttachments.slice(0, MAX_RECENT_ATTACHMENTS);

    // Save to AsyncStorage
    await AsyncStorage.setItem(
      RECENT_ATTACHMENTS_KEY,
      JSON.stringify(trimmedAttachments)
    );
  } catch (error) {
    console.error('Error adding recent attachment:', error);
  }
};

/**
 * Get list of recent attachments
 * @returns Array of recent attachments sorted by most recent first
 */
export const getRecentAttachments = async (): Promise<RecentAttachment[]> => {
  try {
    const attachmentsJson = await AsyncStorage.getItem(RECENT_ATTACHMENTS_KEY);

    if (!attachmentsJson) {
      return [];
    }

    const attachments: RecentAttachment[] = JSON.parse(attachmentsJson);

    // Sort by timestamp (most recent first)
    return attachments.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error getting recent attachments:', error);
    return [];
  }
};

/**
 * Clear all recent attachments
 */
export const clearRecentAttachments = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(RECENT_ATTACHMENTS_KEY);
  } catch (error) {
    console.error('Error clearing recent attachments:', error);
  }
};
