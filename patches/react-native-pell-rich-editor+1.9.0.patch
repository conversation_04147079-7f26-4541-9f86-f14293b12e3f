diff --git a/node_modules/react-native-pell-rich-editor/src/RichEditor.js b/node_modules/react-native-pell-rich-editor/src/RichEditor.js
index 14bea22..6ba9a1b 100755
--- a/node_modules/react-native-pell-rich-editor/src/RichEditor.js
+++ b/node_modules/react-native-pell-rich-editor/src/RichEditor.js
@@ -267,7 +267,8 @@ export default class RichTextEditor extends Component {
           ref={that.setRef}
           onMessage={that.onMessage}
           originWhitelist={['*']}
-          dataDetectorTypes={'none'}
+          dataDetectorTypes={['none']}
+          // dataDetectorTypes={'none'}
           domStorageEnabled={false}
           bounces={false}
           javaScriptEnabled={true}
